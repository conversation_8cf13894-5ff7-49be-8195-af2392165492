require 'redmine'

Rails.logger.info 'Starting Custom PDF Export plugin for Redmine'

Redmine::Plugin.register :redmine_custom_pdf_export do
  name 'Custom PDF Export Plugin'
  author 'AiYuHang'
  description 'A plugin to customize PDF export content for issues with configurable headers, footers, and signature sections'
  version '1.0.0'
  url 'https://redminecn.com'
  author_url 'mailto:<EMAIL>'

  # Plugin settings with default values
  settings :default => {
    'export_mode' => 'detailed',  # detailed or summary
    'company_name' => '',
    'company_logo_url' => '',
    'copyright_text' => '',
    'enable_signature_section' => true,
    'signature_roles' => [],
    'custom_signature_fields' => [],
    'custom_memo_text' => ''
  }, :partial => 'settings/custom_pdf_export_settings'

  # Add permissions
  permission :manage_custom_pdf_export, { :custom_pdf_settings => [:index, :update] }, :require => :admin
end

# Load the hooks and patches
require_relative 'lib/redmine_custom_pdf_export/hooks'
require_relative 'lib/redmine_custom_pdf_export/pdf_customizer'

# Patch the IssuesPdfHelper to use our custom PDF generator
# Rails.application.config.to_prepare do
#   begin
#     unless Redmine::Export::PDF::IssuesPdfHelper.included_modules.include?(RedmineCustomPdfExport::PdfCustomizer)
#       Redmine::Export::PDF::IssuesPdfHelper.send(:prepend, RedmineCustomPdfExport::PdfCustomizer)
#       Rails.logger.info "Successfully patched IssuesPdfHelper with PdfCustomizer"
#     end
#   rescue => e
#     Rails.logger.error "Failed to patch IssuesPdfHelper: #{e.message}"
#     Rails.logger.error e.backtrace.join("\n")
#   end
# end
