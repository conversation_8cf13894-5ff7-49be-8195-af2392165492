<div class="custom-pdf-export-settings">
  <h3><%= l(:label_custom_pdf_export_settings, :default => 'Custom PDF Export Settings') %></h3>
  
  <div class="box tabular">
    <fieldset>
      <legend><%= l(:label_export_mode, :default => 'Export Mode') %></legend>
      <p>
        <%= content_tag :label do %>
          <%= radio_button_tag 'settings[export_mode]', 'detailed', 
                               @settings['export_mode'] == 'detailed' || @settings['export_mode'].blank? %>
          <%= l(:label_detailed_mode, :default => 'Detailed Mode') %>
        <% end %>
        <br>
        <small><%= l(:text_detailed_mode_description, 
                     :default => 'Include complete issue description, subtasks, history comments, and all custom fields.') %></small>
      </p>
      <p>
        <%= content_tag :label do %>
          <%= radio_button_tag 'settings[export_mode]', 'summary', 
                               @settings['export_mode'] == 'summary' %>
          <%= l(:label_summary_mode, :default => 'Summary Mode') %>
        <% end %>
        <br>
        <small><%= l(:text_summary_mode_description, 
                     :default => 'Focus on key milestones and deliverables, suitable for client reporting.') %></small>
      </p>
    </fieldset>

    <fieldset>
      <legend><%= l(:label_company_header, :default => 'Company Header') %></legend>
      <p>
        <%= content_tag :label, l(:field_company_name, :default => 'Company Name') %>
        <%= text_field_tag 'settings[company_name]', @settings['company_name'], 
                           :size => 50, :placeholder => l(:placeholder_company_name, :default => 'Enter your company name') %>
      </p>
      <p>
        <%= content_tag :label, l(:field_company_logo_url, :default => 'Company Logo URL') %>
        <%= text_field_tag 'settings[company_logo_url]', @settings['company_logo_url'], 
                           :size => 50, :placeholder => l(:placeholder_logo_url, :default => 'http://example.com/logo.png') %>
        <br>
        <small><%= l(:text_logo_url_description, 
                     :default => 'URL to your company logo image. Recommended size: 120x40 pixels.') %></small>
      </p>
      <p>
        <%= content_tag :label, l(:field_copyright_text, :default => 'Copyright Text') %>
        <%= text_area_tag 'settings[copyright_text]', @settings['copyright_text'], 
                          :rows => 2, :cols => 50, 
                          :placeholder => l(:placeholder_copyright, :default => '© 2024 Your Company Name. All rights reserved.') %>
      </p>
    </fieldset>

    <fieldset>
      <legend><%= l(:label_signature_section, :default => 'Signature Section') %></legend>
      <p>
        <%= content_tag :label do %>
          <%= check_box_tag 'settings[enable_signature_section]', '1', 
                            @settings['enable_signature_section'] != false %>
          <%= l(:label_enable_signature_section, :default => 'Enable signature section in PDF footer') %>
        <% end %>
      </p>
      
      <div id="signature-options" style="<%= 'display: none;' if @settings['enable_signature_section'] == false %>">
        <p>
          <%= content_tag :label, l(:label_signature_roles, :default => 'Roles for Signature') %>
          <br>
          <% Role.givable.each do |role| %>
            <%= content_tag :label, :class => 'checkbox-label' do %>
              <%= check_box_tag 'settings[signature_roles][]', role.id, 
                                (@settings['signature_roles'] || []).include?(role.id.to_s) %>
              <%= role.name %>
            <% end %>
            <br>
          <% end %>
          <small><%= l(:text_signature_roles_description, 
                       :default => 'Select roles whose members should have signature boxes in the PDF.') %></small>
        </p>

        <p>
          <%= content_tag :label, l(:label_custom_signature_fields, :default => 'Custom Signature Fields') %>
          <br>
          <% IssueCustomField.where(:field_format => 'string').each do |field| %>
            <%= content_tag :label, :class => 'checkbox-label' do %>
              <%= check_box_tag 'settings[custom_signature_fields][]', field.id, 
                                (@settings['custom_signature_fields'] || []).include?(field.id.to_s) %>
              <%= field.name %>
            <% end %>
            <br>
          <% end %>
          <small><%= l(:text_custom_signature_fields_description, 
                       :default => 'Select custom fields that contain names of people who should sign the document.') %></small>
        </p>
      </div>
    </fieldset>

    <fieldset>
      <legend><%= l(:label_custom_memo, :default => 'Custom Memo') %></legend>
      <p>
        <%= content_tag :label, l(:field_custom_memo_text, :default => 'Custom Memo Text') %>
        <%= text_area_tag 'settings[custom_memo_text]', @settings['custom_memo_text'], 
                          :rows => 4, :cols => 60, 
                          :placeholder => l(:placeholder_memo_text, 
                                          :default => 'Enter any additional notes or instructions to be included in the PDF...') %>
        <br>
        <small><%= l(:text_memo_description, 
                     :default => 'This text will be included in all exported PDFs as a memo section.') %></small>
      </p>
    </fieldset>
  </div>

  <div class="preview-section" style="margin-top: 20px;">
    <h4><%= l(:label_preview_settings, :default => 'Preview Settings') %></h4>
    <div class="box">
      <p><strong><%= l(:label_current_settings, :default => 'Current Settings') %>:</strong></p>
      <ul>
        <li><%= l(:field_export_mode, :default => 'Export Mode') %>: 
            <span id="preview-mode"><%= @settings['export_mode'] == 'summary' ? 
                                        l(:label_summary_mode, :default => 'Summary Mode') : 
                                        l(:label_detailed_mode, :default => 'Detailed Mode') %></span></li>
        <li><%= l(:field_company_name, :default => 'Company Name') %>: 
            <span id="preview-company"><%= @settings['company_name'].present? ? @settings['company_name'] : 
                                           l(:text_not_configured, :default => 'Not configured') %></span></li>
        <li><%= l(:label_signature_section, :default => 'Signature Section') %>: 
            <span id="preview-signature"><%= @settings['enable_signature_section'] != false ? 
                                             l(:label_enabled, :default => 'Enabled') : 
                                             l(:label_disabled, :default => 'Disabled') %></span></li>
      </ul>
    </div>
  </div>
</div>

<script type="text/javascript">
  document.addEventListener('DOMContentLoaded', function() {
    // Toggle signature options visibility
    function toggleSignatureOptions() {
      var checkbox = document.querySelector('input[name="settings[enable_signature_section]"]');
      var options = document.getElementById('signature-options');
      if (checkbox && options) {
        options.style.display = checkbox.checked ? 'block' : 'none';
      }
    }
    
    // Update preview
    function updatePreview() {
      var modeRadios = document.querySelectorAll('input[name="settings[export_mode]"]');
      var companyName = document.querySelector('input[name="settings[company_name]"]');
      var signatureCheckbox = document.querySelector('input[name="settings[enable_signature_section]"]');
      
      modeRadios.forEach(function(radio) {
        if (radio.checked) {
          var previewMode = document.getElementById('preview-mode');
          if (previewMode) {
            previewMode.textContent = radio.value === 'summary' ? 
              '<%= l(:label_summary_mode, :default => 'Summary Mode') %>' : 
              '<%= l(:label_detailed_mode, :default => 'Detailed Mode') %>';
          }
        }
      });
      
      if (companyName) {
        var previewCompany = document.getElementById('preview-company');
        if (previewCompany) {
          previewCompany.textContent = companyName.value || '<%= l(:text_not_configured, :default => 'Not configured') %>';
        }
      }
      
      if (signatureCheckbox) {
        var previewSignature = document.getElementById('preview-signature');
        if (previewSignature) {
          previewSignature.textContent = signatureCheckbox.checked ? 
            '<%= l(:label_enabled, :default => 'Enabled') %>' : 
            '<%= l(:label_disabled, :default => 'Disabled') %>';
        }
      }
    }
    
    // Event listeners
    var signatureCheckbox = document.querySelector('input[name="settings[enable_signature_section]"]');
    if (signatureCheckbox) {
      signatureCheckbox.addEventListener('change', function() {
        toggleSignatureOptions();
        updatePreview();
      });
    }
    
    var modeRadios = document.querySelectorAll('input[name="settings[export_mode]"]');
    modeRadios.forEach(function(radio) {
      radio.addEventListener('change', updatePreview);
    });
    
    var companyName = document.querySelector('input[name="settings[company_name]"]');
    if (companyName) {
      companyName.addEventListener('input', updatePreview);
    }
    
    // Initial setup
    toggleSignatureOptions();
  });
</script>
