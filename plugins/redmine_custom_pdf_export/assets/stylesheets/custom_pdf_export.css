/* Custom PDF Export Plugin Styles */

.custom-pdf-export-settings {
  max-width: 800px;
}

.custom-pdf-export-settings fieldset {
  margin-bottom: 20px;
  padding: 15px;
  border: 1px solid #d7d7d7;
  border-radius: 4px;
}

.custom-pdf-export-settings legend {
  font-weight: bold;
  color: #484848;
  padding: 0 10px;
}

.custom-pdf-export-settings .checkbox-label {
  display: inline-block;
  margin-right: 15px;
  font-weight: normal;
}

.custom-pdf-export-settings .checkbox-label input[type="checkbox"] {
  margin-right: 5px;
}

.custom-pdf-export-settings small {
  color: #666;
  font-style: italic;
}

.custom-pdf-export-settings .preview-section {
  background-color: #f8f8f8;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  padding: 15px;
}

.custom-pdf-export-settings .preview-section .box {
  background-color: white;
  padding: 10px;
  border-radius: 3px;
}

.custom-pdf-export-settings .preview-section ul {
  margin: 10px 0;
  padding-left: 20px;
}

.custom-pdf-export-settings .preview-section li {
  margin-bottom: 5px;
}

.custom-pdf-export-settings input[type="text"],
.custom-pdf-export-settings textarea {
  width: 100%;
  max-width: 500px;
}

.custom-pdf-export-settings textarea {
  resize: vertical;
}

.custom-pdf-export-info {
  background-color: #e8f4fd;
  border: 1px solid #b3d9ff;
  border-radius: 4px;
  padding: 10px;
  margin-bottom: 15px;
}

.custom-pdf-export-info p {
  margin: 0;
  color: #2c5aa0;
}

/* Radio button styling */
.custom-pdf-export-settings input[type="radio"] {
  margin-right: 8px;
}

.custom-pdf-export-settings label {
  display: block;
  margin-bottom: 8px;
}

/* Responsive design */
@media (max-width: 768px) {
  .custom-pdf-export-settings {
    max-width: 100%;
  }
  
  .custom-pdf-export-settings input[type="text"],
  .custom-pdf-export-settings textarea {
    max-width: 100%;
  }
  
  .custom-pdf-export-settings .checkbox-label {
    display: block;
    margin-bottom: 8px;
  }
}
