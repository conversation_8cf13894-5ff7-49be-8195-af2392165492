# Custom PDF Export Plugin for Redmine

## 概述

Custom PDF Export Plugin 是一款为 Redmine 平台定制PDF导出功能的插件，提供可配置的PDF文档导出和生成能力。该插件支持预定义导出的PDF文件内容，包括摘要或详情、PDF页眉（公司名称、企业徽标）、动态抽取问题干系人并绘制PDF尾部签字栏、自定义备注等功能。

## 主要功能

### 1. 导出摘要或详情
- **详情模式**：完整呈现问题描述、子任务树、历史评论，满足技术团队回溯需求
- **精简模式**：聚焦关键里程碑、交付物清单，适配客户侧汇报场景

### 2. 嵌入企业页眉
- 自动嵌入企业标识（公司徽标、名称）
- 支持版权声明和项目信息
- 自动生成文档版本号和日期

### 3. 签署与审计
- 在PDF尾部插入定制化签署栏
- 支持基于Redmine角色的签字栏生成
- 支持自定义用户字段（如审核人、客户代表等）
- 自动包含问题相关干系人

### 4. 自定义备注
- 支持在PDF中添加标准说明文本
- 可用于免责声明、使用须知等

## 适用场景

- **跨组织协作**：生成标准化的交付文档
- **文档标准化及合规**：统一企业文档格式
- **业务交付与审计**：提供签字确认功能

## 安装方法

1. 将插件文件复制到 Redmine 的 `plugins` 目录：
   ```bash
   cd /path/to/redmine
   cp -r redmine_custom_pdf_export plugins/
   ```

2. 重启 Redmine 服务器：
   ```bash
   # 如果使用 Passenger
   touch tmp/restart.txt
   
   # 如果使用其他服务器
   sudo service redmine restart
   ```

3. 在 Redmine 管理界面中配置插件：
   - 进入 `管理` → `插件`
   - 找到 "Custom PDF Export Plugin"
   - 点击 `配置` 进行设置

## 配置说明

### 导出模式设置
- **详情模式**：包含完整的问题信息、历史记录和评论
- **精简模式**：仅包含关键信息，适合对外交付

### 企业页眉设置
- **公司名称**：显示在PDF页眉的公司名称
- **公司徽标URL**：徽标图片的网络地址（建议尺寸：120x40像素）
- **版权声明**：显示在页眉的版权信息

### 签字栏设置
- **启用签字栏**：是否在PDF底部生成签字区域
- **签字角色**：选择哪些项目角色需要签字
- **自定义签字字段**：选择包含签字人信息的自定义字段

### 自定义备注
- 在PDF中添加标准说明文本或注释

## 使用方法

1. 配置完成后，正常导出问题PDF即可
2. 在问题详情页面点击PDF导出
3. 或在问题列表页面批量导出PDF
4. 生成的PDF将包含您配置的自定义内容

## 技术要求

- Redmine 4.0+
- Ruby 2.6+
- Rails 5.2+

## 版本历史

### v1.0.0
- 初始版本发布
- 支持详情/精简模式切换
- 企业页眉定制功能
- 签字栏生成功能
- 多语言支持（中文/英文）

## 支持与反馈

如有问题或建议，请联系：
- 邮箱：<EMAIL>
- 网站：https://redminecn.com

## 许可证

本插件基于 GPL v2 许可证发布。
