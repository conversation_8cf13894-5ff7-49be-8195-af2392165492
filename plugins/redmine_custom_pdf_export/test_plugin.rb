#!/usr/bin/env ruby

# Simple test script to verify plugin structure and syntax
puts "Testing Custom PDF Export Plugin..."

# Test 1: Check plugin files exist
required_files = [
  'init.rb',
  'lib/redmine_custom_pdf_export/pdf_customizer.rb',
  'lib/redmine_custom_pdf_export/hooks.rb',
  'app/views/settings/_custom_pdf_export_settings.html.erb',
  'config/locales/en.yml',
  'config/locales/zh.yml'
]

puts "\n1. Checking required files..."
required_files.each do |file|
  if File.exist?(file)
    puts "  ✓ #{file}"
  else
    puts "  ✗ #{file} - MISSING"
  end
end

# Test 2: Check Ruby syntax
puts "\n2. Checking Ruby syntax..."
ruby_files = Dir.glob('**/*.rb')
ruby_files.each do |file|
  result = `ruby -c #{file} 2>&1`
  if $?.success?
    puts "  ✓ #{file}"
  else
    puts "  ✗ #{file} - SYNTAX ERROR"
    puts "    #{result}"
  end
end

# Test 3: Check YAML syntax
puts "\n3. Checking YAML syntax..."
yaml_files = Dir.glob('**/*.yml')
yaml_files.each do |file|
  begin
    require 'yaml'
    YAML.load_file(file)
    puts "  ✓ #{file}"
  rescue => e
    puts "  ✗ #{file} - YAML ERROR"
    puts "    #{e.message}"
  end
end

# Test 4: Check ERB files exist
puts "\n4. Checking ERB files..."
erb_files = Dir.glob('**/*.erb')
erb_files.each do |file|
  if File.exist?(file) && File.readable?(file)
    puts "  ✓ #{file} (syntax check requires Rails environment)"
  else
    puts "  ✗ #{file} - NOT READABLE"
  end
end

puts "\nPlugin structure test completed!"
puts "\nNext steps:"
puts "1. Restart your Redmine server"
puts "2. Go to Administration → Plugins"
puts "3. Look for 'Custom PDF Export Plugin'"
puts "4. Click 'Configure' to set up the plugin"
