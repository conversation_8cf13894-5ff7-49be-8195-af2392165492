module RedmineCustomPdfExport
  class Hooks < Redmine::Hook::ViewListener
    # Add custom CSS for the plugin settings
    def view_layouts_base_html_head(context={})
      stylesheet_link_tag('custom_pdf_export', :plugin => 'redmine_custom_pdf_export')
    end

    # Add menu item for plugin settings (if needed)
    def view_layouts_base_content(context={})
      return '' unless User.current.admin?
      
      content = ''
      if context[:controller].is_a?(SettingsController) && context[:controller].action_name == 'plugin'
        content << content_tag(:div, :class => 'custom-pdf-export-info') do
          content_tag(:p, l(:text_custom_pdf_export_info, :default => 'Configure PDF export settings below to customize issue PDF output.'))
        end
      end
      content.html_safe
    end
  end
end
