# Custom PDF Export Plugin 安装指南

## 安装步骤

### 1. 确认环境要求
- Redmine 4.0+
- Ruby 2.6+
- Rails 5.2+

### 2. 安装插件

插件已经放置在正确的位置：
```
plugins/redmine_custom_pdf_export/
```

### 3. 重启Redmine服务器

根据您的部署方式选择相应的重启方法：

#### 使用Passenger
```bash
touch tmp/restart.txt
```

#### 使用Puma/Unicorn
```bash
sudo systemctl restart redmine
# 或
sudo service redmine restart
```

#### 开发环境
```bash
rails server
```

### 4. 验证安装

1. 登录Redmine管理员账户
2. 进入 `管理` → `插件`
3. 查看是否显示 "Custom PDF Export Plugin"
4. 点击 `配置` 进行设置

## 配置说明

### 基本配置

1. **导出模式**
   - 详情模式：包含完整信息
   - 精简模式：仅关键信息

2. **企业页眉**
   - 公司名称：显示在PDF页眉
   - 公司徽标URL：徽标图片地址
   - 版权声明：版权信息

3. **签字栏**
   - 启用签字栏：是否显示签字区域
   - 签字角色：选择需要签字的角色
   - 自定义签字字段：基于自定义字段的签字人

4. **自定义备注**
   - 在PDF中添加标准说明

### 使用方法

配置完成后：
1. 进入任意问题详情页
2. 点击PDF导出按钮
3. 生成的PDF将包含您配置的自定义内容

## 故障排除

### 插件未显示
- 检查文件权限
- 确认重启了服务器
- 查看Redmine日志

### PDF导出异常
- 检查插件配置
- 验证徽标URL可访问性
- 确认自定义字段设置

### 权限问题
- 确保以正确用户身份运行Redmine
- 检查插件目录权限

## 卸载插件

如需卸载：
1. 删除插件目录：`rm -rf plugins/redmine_custom_pdf_export`
2. 重启Redmine服务器

## 技术支持

如有问题，请联系：
- 邮箱：<EMAIL>
- 网站：https://redminecn.com
