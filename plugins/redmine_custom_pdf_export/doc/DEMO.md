# Custom PDF Export Plugin 演示

## 功能演示

### 1. 插件安装后的效果

安装插件后，您可以在 Redmine 管理界面看到新的配置选项：

1. 进入 `管理` → `插件`
2. 找到 "Custom PDF Export Plugin"
3. 点击 `配置` 按钮

### 2. 配置界面功能

#### 导出模式设置
- **详情模式**：包含完整的问题信息、历史记录、评论等
- **精简模式**：仅显示关键信息，适合客户报告

#### 企业页眉设置
- **公司名称**：在PDF页眉显示公司名称
- **公司徽标URL**：添加公司徽标到PDF页眉
- **版权声明**：添加版权信息

#### 签字栏设置
- **启用签字栏**：在PDF底部生成签字区域
- **签字角色**：选择需要签字的项目角色
- **自定义签字字段**：基于自定义字段添加签字人

#### 自定义备注
- 在PDF中添加标准说明文本

### 3. 使用效果对比

#### 原始PDF导出
- 标准的Redmine问题信息
- 无企业标识
- 无签字栏

#### 定制PDF导出
- 带有企业徽标和名称的页眉
- 可选择详情或精简模式
- 底部包含相关人员签字栏
- 包含自定义备注内容

### 4. 适用场景示例

#### 场景1：技术团队内部使用
- 选择"详情模式"
- 包含完整的问题历史和技术细节
- 用于问题跟踪和技术回溯

#### 场景2：客户交付文档
- 选择"精简模式"
- 添加公司徽标和版权声明
- 包含客户代表签字栏
- 用于正式交付确认

#### 场景3：审计和合规
- 选择"详情模式"
- 包含审核人员签字栏
- 添加合规说明备忘录
- 用于审计跟踪

### 5. 配置建议

#### 基础配置
```
导出模式: 详情模式
公司名称: 您的公司名称
启用签字栏: 是
```

#### 高级配置
```
公司徽标URL: https://your-domain.com/logo.png
版权声明: © 2024 您的公司名称。保留所有权利。
签字角色: 项目经理, 开发者, 测试人员
自定义签字字段: 客户代表, 审核人
备忘录: 本文档为项目交付确认单，请相关人员签字确认。
```

### 6. 注意事项

1. **徽标要求**：建议使用120x40像素的PNG或JPG格式图片
2. **网络访问**：确保Redmine服务器能访问徽标URL
3. **权限设置**：只有管理员可以配置插件设置
4. **兼容性**：支持Redmine 4.0+版本

### 7. 故障排除

#### 问题：PDF导出没有应用自定义设置
- 检查插件是否正确安装
- 确认已重启Redmine服务器
- 验证插件配置是否保存成功

#### 问题：徽标不显示
- 检查徽标URL是否可访问
- 确认图片格式和尺寸
- 检查网络连接

#### 问题：签字栏为空
- 确认选择的角色在项目中有成员
- 检查自定义字段是否有值
- 验证权限设置
