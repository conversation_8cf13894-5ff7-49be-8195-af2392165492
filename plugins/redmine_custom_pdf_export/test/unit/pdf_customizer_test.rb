require File.expand_path('../../test_helper', __FILE__)

class PdfCustomizerTest < ActiveSupport::TestCase
  fixtures :projects, :users, :roles, :members, :member_roles,
           :issues, :issue_statuses, :trackers, :enumerations,
           :custom_fields, :custom_values

  def setup
    @project = Project.find(1)
    @issue = Issue.find(1)
    @user = User.find(2)
    User.current = @user
  end

  def test_pdf_customizer_module_included
    assert Redmine::Export::PDF::IssuesPdfHelper.included_modules.include?(RedmineCustomPdfExport::PdfCustomizer)
  end

  def test_issue_to_pdf_custom_with_default_settings
    # Test with default settings
    Setting.plugin_redmine_custom_pdf_export = {
      'export_mode' => 'detailed',
      'enable_signature_section' => true
    }
    
    pdf_content = @issue.to_pdf
    assert_not_nil pdf_content
    assert pdf_content.length > 0
  end

  def test_issue_to_pdf_custom_with_summary_mode
    # Test with summary mode
    Setting.plugin_redmine_custom_pdf_export = {
      'export_mode' => 'summary',
      'enable_signature_section' => false
    }
    
    pdf_content = @issue.to_pdf
    assert_not_nil pdf_content
    assert pdf_content.length > 0
  end

  def test_get_issue_stakeholders
    # This would test the stakeholder extraction logic
    # Implementation depends on your specific test data
  end

  private

  def pdf_helper
    helper = Object.new
    helper.extend(Redmine::Export::PDF::IssuesPdfHelper)
    helper.extend(RedmineCustomPdfExport::PdfCustomizer)
    helper
  end
end
